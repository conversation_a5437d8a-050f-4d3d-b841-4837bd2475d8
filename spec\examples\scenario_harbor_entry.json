{"schema_version": "1.0.0", "$id": "spec/schemas/scenario.schema.json", "identity": {"scenario_id": "harbor_entry_demo", "name": "Harbor Entry with Enhanced Control Systems"}, "crs": {"type": "LOCAL_ENU", "origin": {"lon": -0.001, "lat": 51.0, "h": 0.0}, "theta0_rad": 0.0}, "simulation": {"t0": 0.0, "t_end": 300.0, "t_step": 0.2, "integrator": "rk4", "seed": 42}, "initial_conditions": {"state": {"x": -500.0, "y": -200.0, "psi_rad": 0.785, "u": 3.0, "v": 0.0, "r": 0.0}}, "control": {"mode": "manual", "schedule": [{"t": 0.0, "rpm": 40.0, "rudder_rad": 0.0, "throttle": 0.6, "notes": "approach"}, {"t": 30.0, "rpm": 40.0, "rudder_rad": 0.1, "throttle": 0.6, "notes": "course_correction"}, {"t": 60.0, "rpm": 30.0, "rudder_rad": 0.05, "throttle": 0.4, "notes": "slow_approach"}, {"t": 90.0, "rpm": 20.0, "rudder_rad": 0.15, "throttle": 0.3, "notes": "harbor_entry"}, {"t": 120.0, "rpm": 15.0, "rudder_rad": 0.2, "throttle": 0.2, "notes": "tight_turn"}, {"t": 150.0, "rpm": 10.0, "rudder_rad": 0.1, "throttle": 0.15, "notes": "final_approach"}, {"t": 180.0, "rpm": 5.0, "rudder_rad": 0.0, "throttle": 0.1, "notes": "docking_speed"}, {"t": 210.0, "rpm": 0.0, "rudder_rad": -0.05, "throttle": 0.0, "notes": "stop_engines"}, {"t": 240.0, "rpm": -5.0, "rudder_rad": 0.0, "throttle": 0.0, "notes": "reverse_thrust"}]}, "environment": {"wind": {"speed": 8.0, "dir_from_rad": 2.356}, "current": {"speed": 0.4, "dir_to_rad": 1.047}, "bathymetry": {"type": "harbor_profile", "depth_schedule": [{"t": 0.0, "depth_m": 25.0}, {"t": 120.0, "depth_m": 15.0}, {"t": 240.0, "depth_m": 8.0}]}}, "outputs": {"per_tick": {"decimation": 1}, "websocket": {"enabled": false, "decimation": 3}}, "termination_bounds": {"x_min": -600.0, "x_max": 200.0, "y_min": -300.0, "y_max": 100.0}, "enhanced_control_systems": {"description": "Potential harbor entry control systems", "bow_thrusters": {"max_force_kn": 150.0, "position_x_m": 140.0, "control_schedule": [{"t": 120.0, "force_kn": 50.0, "angle_rad": 1.57}, {"t": 180.0, "force_kn": 30.0, "angle_rad": 0.0}, {"t": 240.0, "force_kn": 0.0, "angle_rad": 0.0}]}, "stern_thrusters": {"max_force_kn": 100.0, "position_x_m": -140.0, "control_schedule": [{"t": 150.0, "force_kn": -25.0, "angle_rad": -1.57}, {"t": 210.0, "force_kn": 0.0, "angle_rad": 0.0}]}, "dynamic_positioning": {"enabled": false, "target_position": {"x": 0.0, "y": 0.0, "psi_rad": 0.0}, "control_gains": {"kp_pos": 1000.0, "kd_pos": 500.0, "kp_heading": 50000.0}}, "autopilot_modes": ["manual", "heading_hold", "track_follow", "station_keeping", "dynamic_positioning"]}, "notes": "Demonstrates harbor entry maneuvers with enhanced control capabilities including thrusters and variable throttle"}