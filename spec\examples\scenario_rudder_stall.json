{"schema_version": "1.0.0", "$id": "spec/schemas/scenario.schema.json", "identity": {"scenario_id": "rudder_stall_demo", "name": "Rudder Stall Behavior Demonstration"}, "crs": {"type": "LOCAL_ENU", "origin": {"lon": -0.001, "lat": 51.0, "h": 0.0}, "theta0_rad": 0.0}, "simulation": {"t0": 0.0, "t_end": 100.0, "t_step": 0.1, "integrator": "rk4", "seed": 42}, "initial_conditions": {"state": {"x": 0.0, "y": 0.0, "psi_rad": 0.0, "u": 10.0, "v": 0.0, "r": 0.0}}, "control": {"mode": "manual", "schedule": [{"t": 0.0, "rpm": 50.0, "rudder_rad": 0.0}, {"t": 10.0, "rpm": 50.0, "rudder_rad": 0.1}, {"t": 20.0, "rpm": 50.0, "rudder_rad": 0.2}, {"t": 30.0, "rpm": 50.0, "rudder_rad": 0.35}, {"t": 40.0, "rpm": 50.0, "rudder_rad": 0.5}, {"t": 50.0, "rpm": 50.0, "rudder_rad": 0.35}, {"t": 60.0, "rpm": 50.0, "rudder_rad": 0.2}, {"t": 70.0, "rpm": 50.0, "rudder_rad": 0.1}, {"t": 80.0, "rpm": 50.0, "rudder_rad": 0.0}, {"t": 90.0, "rpm": 50.0, "rudder_rad": -0.35}]}, "environment": {"wind": {"speed": 5.0, "dir_from_rad": 0.0}, "current": {"speed": 0.0, "dir_to_rad": 0.0}, "bathymetry": {"type": "constant_depth", "depth_m": 100.0}}, "outputs": {"per_tick": {"decimation": 1}, "websocket": {"enabled": false, "decimation": 2}}, "termination_bounds": {"x_min": -5000.0, "x_max": 5000.0, "y_min": -5000.0, "y_max": 5000.0}, "notes": "Demonstrates rudder stall effects at high deflection angles and recovery behavior"}