{"schema_version": "1.0.0", "$id": "spec/schemas/scenario.schema.json", "identity": {"scenario_id": "thruster_demo", "name": "Bow/Stern Thruster Capabilities Demonstration"}, "crs": {"type": "LOCAL_ENU", "origin": {"lon": -0.001, "lat": 51.0, "h": 0.0}, "theta0_rad": 0.0}, "simulation": {"t0": 0.0, "t_end": 120.0, "t_step": 0.2, "integrator": "rk4", "seed": 42}, "initial_conditions": {"state": {"x": 0.0, "y": 0.0, "psi_rad": 0.0, "u": 0.0, "v": 0.0, "r": 0.0}}, "control": {"mode": "manual", "schedule": [{"t": 0.0, "rpm": 0.0, "rudder_rad": 0.0, "notes": "stationary_start"}, {"t": 10.0, "rpm": 0.0, "rudder_rad": 0.0, "bow_thruster_force": 60000.0, "bow_thruster_angle": 0.0, "notes": "bow_thruster_starboard"}, {"t": 20.0, "rpm": 0.0, "rudder_rad": 0.0, "bow_thruster_force": 0.0, "notes": "stop_bow_thruster"}, {"t": 30.0, "rpm": 0.0, "rudder_rad": 0.0, "stern_thruster_force": -50000.0, "stern_thruster_angle": 0.0, "notes": "stern_thruster_port"}, {"t": 40.0, "rpm": 0.0, "rudder_rad": 0.0, "stern_thruster_force": 0.0, "notes": "stop_stern_thruster"}, {"t": 50.0, "rpm": 0.0, "rudder_rad": 0.0, "bow_thruster_force": 40000.0, "bow_thruster_angle": 1.57, "notes": "bow_thruster_forward"}, {"t": 60.0, "rpm": 0.0, "rudder_rad": 0.0, "bow_thruster_force": 0.0, "notes": "stop_bow_thruster"}, {"t": 70.0, "rpm": 0.0, "rudder_rad": 0.0, "stern_thruster_force": 40000.0, "stern_thruster_angle": -1.57, "notes": "stern_thruster_aft"}, {"t": 80.0, "rpm": 0.0, "rudder_rad": 0.0, "stern_thruster_force": 0.0, "notes": "stop_stern_thruster"}, {"t": 90.0, "rpm": 0.0, "rudder_rad": 0.0, "bow_thruster_force": 30000.0, "bow_thruster_angle": 1.57, "stern_thruster_force": -30000.0, "stern_thruster_angle": -1.57, "notes": "combined_thrusters"}, {"t": 110.0, "rpm": 0.0, "rudder_rad": 0.0, "bow_thruster_force": 0.0, "stern_thruster_force": 0.0, "notes": "stop_all_thrusters"}]}, "environment": {"wind": {"speed": 2.0, "dir_from_rad": 0.0}, "current": {"speed": 0.2, "dir_to_rad": 1.57}, "bathymetry": {"type": "constant_depth", "depth_m": 20.0}}, "outputs": {"per_tick": {"decimation": 1}, "websocket": {"enabled": false, "decimation": 2}}, "termination_bounds": {"x_min": -200.0, "x_max": 200.0, "y_min": -200.0, "y_max": 200.0}, "notes": "Demonstrates individual and combined bow/stern thruster operations for precise vessel positioning"}